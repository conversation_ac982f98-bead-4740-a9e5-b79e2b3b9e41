import { AgentSettings, AgentStatus, CollateralType } from "../../../../lib/fasset/AssetManagerTypes";
import { testChainInfo } from "../../../../lib/test-utils/actors/TestChainInfo";
import { AssetManagerInitSettings, newAssetManager } from "../../../../lib/test-utils/fasset/CreateAssetManager";
import { MockChain, MockChainWallet } from "../../../../lib/test-utils/fasset/MockChain";
import { MockFlareDataConnectorClient } from "../../../../lib/test-utils/fasset/MockFlareDataConnectorClient";
import { expectRevert, time } from "../../../../lib/test-utils/test-helpers";
import { createTestAgent, createTestCollaterals, createTestContracts, createTestSettings, TestSettingsContracts } from "../../../../lib/test-utils/test-settings";
import { getTestFile, loadFixtureCopyVars } from "../../../../lib/test-utils/test-suite-helpers";
import { assertWeb3Equal } from "../../../../lib/test-utils/web3assertions";
import { AttestationHelper } from "../../../../lib/underlying-chain/AttestationHelper";
import { EventArgs } from "../../../../lib/utils/events/common";
import { filterEvents, requiredEventArgs } from "../../../../lib/utils/events/truffle";
import { BN_ZERO, BNish, toBN, toBNExp, toWei, ZERO_ADDRESS } from "../../../../lib/utils/helpers";
import { AgentVaultInstance, ERC20MockInstance, FAssetInstance, IIAssetManagerInstance, WNatMockInstance } from "../../../../typechain-truffle";
import { CollateralReserved } from "../../../../typechain-truffle/IIAssetManager";

contract(`LiquidationOverLiquidationPOC.sol; ${getTestFile(__filename)}; Over-liquidation POC tests`, accounts => {
    const governance = accounts[10];
    const assetManagerController = accounts[11];
    let contracts: TestSettingsContracts;
    let assetManager: IIAssetManagerInstance;
    let fAsset: FAssetInstance;
    let wNat: WNatMockInstance;
    let usdc: ERC20MockInstance;
    let settings: AssetManagerInitSettings;
    let collaterals: CollateralType[];
    let chain: MockChain;
    let wallet: MockChainWallet;
    let flareDataConnectorClient: MockFlareDataConnectorClient;
    let attestationProvider: AttestationHelper;

    // addresses
    const agentOwner1 = accounts[20];
    const minterAddress1 = accounts[30];
    const liquidatorAddress1 = accounts[60];
    const noExecutorAddress = ZERO_ADDRESS;

    // addresses on mock underlying chain can be any string, as long as it is unique
    const underlyingAgent1 = "Agent1";
    const underlyingMinter1 = "Minter1";

    function createAgent(owner: string, underlyingAddress: string, options?: Partial<AgentSettings>) {
        const vaultCollateralToken = options?.vaultCollateralToken ?? usdc.address;
        return createTestAgent({ assetManager, settings, chain, wallet, attestationProvider }, owner, underlyingAddress, vaultCollateralToken, options);
    }

    async function depositCollateral(owner: string, agentVault: AgentVaultInstance, amount: BN, token: ERC20MockInstance = usdc) {
        await token.mintAmount(owner, amount);
        await token.approve(agentVault.address, amount, { from: owner });
        await agentVault.depositCollateral(token.address, amount, { from: owner });
    }

    async function depositAndMakeAgentAvailable(agentVault: AgentVaultInstance, owner: string, fullAgentCollateral: BN = toWei(3e8)) {
        await depositCollateral(owner, agentVault, fullAgentCollateral);
        await agentVault.buyCollateralPoolTokens({ from: owner, value: fullAgentCollateral });  // add pool collateral and agent pool tokens
        await assetManager.makeAgentAvailable(agentVault.address, { from: owner });
    }

    async function reserveCollateral(agentVault: AgentVaultInstance, minterAddress: string, lots: BNish) {
        const agentInfo = await assetManager.getAgentInfo(agentVault.address);
        const crFee = await assetManager.collateralReservationFee(lots);
        const resAg = await assetManager.reserveCollateral(agentVault.address, lots, agentInfo.feeBIPS, noExecutorAddress, { from: minterAddress, value: crFee });
        const crt = requiredEventArgs(resAg, 'CollateralReserved');
        return crt;
    }

    async function performMinting(crt: EventArgs<CollateralReserved>, underlyingMinterAddress: string) {
        chain.mint(underlyingMinterAddress, toBNExp(10000, 18));
        const paymentAmount = crt.valueUBA.add(crt.feeUBA);
        const txHash = await wallet.addTransaction(underlyingMinterAddress, crt.paymentAddress, paymentAmount, crt.paymentReference);
        const proof = await attestationProvider.provePayment(txHash, underlyingMinterAddress, crt.paymentAddress);
        const res = await assetManager.executeMinting(proof, crt.collateralReservationId, { from: crt.minter });
        return requiredEventArgs(res, 'MintingExecuted');
    }

    async function mint(agentVault: AgentVaultInstance, underlyingMinterAddress: string, minterAddress: string, lots: BNish = 3) {
        // minter
        const crt = await reserveCollateral(agentVault, minterAddress, lots);
        return await performMinting(crt, underlyingMinterAddress);
    }

    async function initialize() {
        const ci = testChainInfo.eth;
        contracts = await createTestContracts(governance);
        // save some contracts as globals
        ({ wNat } = contracts);
        usdc = contracts.stablecoins.USDC;
        // create mock chain and attestation provider
        chain = new MockChain(await time.latest());
        wallet = new MockChainWallet(chain);
        flareDataConnectorClient = new MockFlareDataConnectorClient(contracts.fdcHub, contracts.relay, { [ci.chainId]: chain }, 'auto');
        attestationProvider = new AttestationHelper(flareDataConnectorClient, chain, ci.chainId);
        // create asset manager
        collaterals = createTestCollaterals(contracts, ci);
        settings = createTestSettings(contracts, ci);
        [assetManager, fAsset] = await newAssetManager(governance, assetManagerController, ci.name, ci.symbol, ci.decimals, settings, collaterals, ci.assetName, ci.assetSymbol);
        return { contracts, wNat, usdc, chain, wallet, flareDataConnectorClient, attestationProvider, collaterals, settings, assetManager, fAsset };
    }

    beforeEach(async () => {
        ({ contracts, wNat, usdc, chain, wallet, flareDataConnectorClient, attestationProvider, collaterals, settings, assetManager, fAsset } = await loadFixtureCopyVars(initialize));
    });

    it("POC: maxLiquidationAmountAMG over-liquidation bug verification", async () => {
        // Setup scenario from Issue.md:
        // fullCollateral=120e18, mintedAMG=109e18, amgToTokenWeiPrice=1,
        // current CR_BIPS=11000 (underwater), targetRatioBIPS=13000 (min CR to achieve)

        const agentVault = await createAgent(agentOwner1, underlyingAgent1);

        // Set up collateral: Use same amounts as working liquidation tests
        const fullAgentCollateral = toWei(3e6);
        await depositAndMakeAgentAvailable(agentVault, agentOwner1, fullAgentCollateral);

        // Mint a reasonable amount to test liquidation
        const lots = 6; // Use same as existing tests
        const minted = await mint(agentVault, underlyingMinter1, minterAddress1, lots);

        // Get initial agent info
        const initialInfo = await assetManager.getAgentInfo(agentVault.address);
        console.log("Initial minted AMG:", initialInfo.mintedUBA.toString());
        console.log("Initial vault collateral:", initialInfo.totalVaultCollateralWei.toString());

        // Change price to create underwater position (exact same as working test)
        const assetName = await fAsset.symbol();
        await contracts.priceStore.setCurrentPrice(assetName, toBNExp(8, 8), 0);
        await contracts.priceStore.setCurrentPriceFromTrustedProviders(assetName, toBNExp(8, 8), 0);

        // Start liquidation
        await assetManager.startLiquidation(agentVault.address);

        // Get agent info after liquidation starts
        const liquidationInfo = await assetManager.getAgentInfo(agentVault.address);
        console.log("Agent status:", liquidationInfo.status.toString());
        console.log("Max liquidation amount UBA:", liquidationInfo.maxLiquidationAmountUBA.toString());
        console.log("Minted UBA:", liquidationInfo.mintedUBA.toString());

        // Calculate liquidation amounts
        const mintedAMG = toBN(liquidationInfo.mintedUBA);
        const maxLiquidationAMG = toBN(liquidationInfo.maxLiquidationAmountUBA);

        console.log("Minted AMG:", mintedAMG.toString());
        console.log("Max liquidation AMG:", maxLiquidationAMG.toString());

        // ASSERTION 1: Verify liquidation amount is reasonable (not over-liquidating)
        // The liquidation amount should be less than the total minted amount
        assert(maxLiquidationAMG.lte(mintedAMG),
            `Liquidation amount should not exceed minted amount. Max liquidation: ${maxLiquidationAMG.toString()}, Minted: ${mintedAMG.toString()}`);

        // ASSERTION 2: Verify the liquidation amount is not excessively high
        const liquidationPercentage = maxLiquidationAMG.mul(toBN(100)).div(mintedAMG);
        console.log("Liquidation percentage of minted AMG:", liquidationPercentage.toString());

        // Check if liquidation is excessive (>80% would indicate potential over-liquidation)
        const isOverLiquidating = liquidationPercentage.gt(toBN(80));
        console.log("Is over-liquidating (>80%):", isOverLiquidating);

        if (isOverLiquidating) {
            console.log("⚠️  POTENTIAL BUG DETECTED: Liquidation amount seems excessive");
        }

        // ASSERTION 3: Simulate the liquidation if there's an amount to liquidate
        if (maxLiquidationAMG.gt(toBN(0))) {
            // Transfer f-assets to liquidator
            await fAsset.transfer(liquidatorAddress1, maxLiquidationAMG, { from: minterAddress1 });

            // Perform liquidation
            const liquidationResult = await assetManager.liquidate(agentVault.address, maxLiquidationAMG, { from: liquidatorAddress1 });

            // Get post-liquidation info
            const postLiquidationInfo = await assetManager.getAgentInfo(agentVault.address);
            console.log("Post-liquidation minted UBA:", postLiquidationInfo.mintedUBA.toString());
            console.log("Post-liquidation vault collateral:", postLiquidationInfo.totalVaultCollateralWei.toString());

            // Calculate how much was actually liquidated
            const actualLiquidated = mintedAMG.sub(toBN(postLiquidationInfo.mintedUBA));
            console.log("Actually liquidated:", actualLiquidated.toString());

            // Verify liquidation was successful
            assert(actualLiquidated.gt(toBN(0)), "Liquidation should have reduced minted amount");

            console.log("✓ Liquidation completed successfully");
        }

        console.log("✓ POC COMPLETE: Liquidation behavior analyzed");
    });
});
