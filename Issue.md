Calculation Leading to Potential Over-Liquidation
Severity: High

Category: Unfair Liquidation Vulnerabilities, Liquidation Calculation Vulnerabilities (per primer)

Description: The function maxLiquidationAmountAMG in the Liquidation library calculates the amount of minted AMG (asset-minted units) that needs to be liquidated to bring an agent's collateral ratio (CR) back to a target ratio (e.g., minCollateralRatioBIPS or safetyMinCollateralRatioBIPS). The formula used is incorrect, leading to an overestimation of the liquidation amount. This allows liquidators to liquidate more AMG than necessary, reducing the agent's collateral excessively and potentially causing unfair losses for the borrower. The issue arises because the formula does not align with the standard derivation for liquidation amounts and uses totalBackedAMG in a way that inflates the result, breaking the invariant that liquidation should only reduce AMG enough to achieve the target CR.
Impact:

Borrowers lose more collateral than needed, as liquidators can liquidate up to the inflated amount.
Liquidators may profit excessively if they receive a premium on liquidated assets.
Griefing potential: Malicious liquidators could exploit this to harm borrowers by liquidating more than required.
Breaks the lending invariant: healthScoreAfter > healthScoreBefore (post-liquidation CR should be close to target, not excessively higher).

Root Cause: The formula totalBackedAMG.mulDivRoundUp(targetRatioBIPS - _collateralRatioBIPS, targetRatioBIPS - _factorBIPS) does not correctly compute the AMG amount needed to reach the target CR. It uses totalBackedAMG (likely collateral value divided by price, representing maximum backable AMG at CR=10000 BIPS) incorrectly, leading to a numerator/denominator mismatch compared to the standard liquidation formula.
Standard Formula Derivation (for reference):

Collateral ratio (CR_BIPS) = (fullCollateral * 10000) / (mintedAMG * amgToTokenWeiPrice).
To reach target CR (targetRatioBIPS), solve for liquidatedAMG such that:

targetRatioBIPS = (fullCollateral * 10000) / ((mintedAMG - liquidatedAMG) * amgToTokenWeiPrice).
Rearrange: liquidatedAMG = mintedAMG - (fullCollateral * 10000 / (targetRatioBIPS * amgToTokenWeiPrice)).


The code’s formula uses totalBackedAMG (collateral-backed AMG at CR=10000) and an incorrect ratio difference, leading to a larger liquidatedAMG.

Simulation Example:

Assume: amgToTokenWeiPrice=1, fullCollateral=120e18, mintedAMG=109e18, current CR_BIPS=11000 (underwater), _factorBIPS=10000 (liquidation threshold), targetRatioBIPS=13000 (min CR to achieve).
Expected: Liquidate ~16.79e18 AMG to reach CR=13000.

remainingAMG = fullCollateral * 10000 / (targetRatioBIPS * price) = 120e18 * 10000 / (13000 * 1) ≈ 92.31e18.
liquidatedAMG = mintedAMG - remainingAMG = 109e18 - 92.31e18 ≈ 16.79e18.
New CR = (120e18 * 10000) / (92.31e18 * 1) ≈ 13000 (target achieved).


Code’s calculation: Assume totalBackedAMG = fullCollateral / price = 120e18 (backed at CR=10000).

liquidatedAMG = 120e18 * (13000-11000)/(13000-10000) = 120e18 * 2000/3000 = 80e18.
Remaining AMG = 109e18 - 80e18 = 29e18.
New CR = (120e18 * 10000) / (29e18 * 1) ≈ 41379 BIPS (far exceeds 13000, over-liquidated).


Issue: Liquidates 80e18 instead of 16.79e18, leaving borrower with much less AMG and an unnecessarily high CR, losing significant collateral value.

PoC Sketch:

Setup: Agent with vaultCollateral=120e18, mintedAMG=109e18, amgToTokenWeiPrice=1, CR_BIPS=11000, targetRatioBIPS=13000, _factorBIPS=10000.
Call maxLiquidationAmountAMG for vault collateral.
Code returns 80e18 AMG to liquidate.
Liquidator liquidates 80e18 AMG, leaving 29e18 AMG, new CR=41379 BIPS (overkill).
Borrower loses ~63.21e18 more AMG worth of collateral than needed.

Fix: Replace the formula with the correct derivation:
solidityuint256 remainingAMG = fullCollateral.mulDiv(10000, targetRatioBIPS * amgToTokenWeiPrice, Math.Rounding.Floor);
uint256 maxLiquidatedAMG = _agent.mintedAMG > remainingAMG ? _agent.mintedAMG - remainingAMG : 0;
return Math.min(maxLiquidatedAMG, _agent.mintedAMG);
Vulnerable Code
Below is the specific code from the Liquidation library, highlighting the problematic function maxLiquidationAmountAMG and the vulnerable formula:
solidity// Calculate the amount of liquidation that gets agent to safety.
// assumed: agentStatus == LIQUIDATION/FULL_LIQUIDATION
function maxLiquidationAmountAMG(
    Agent.State storage _agent,
    uint256 _collateralRatioBIPS,
    uint256 _factorBIPS,
    Collateral.Kind _collateralKind
)
    internal view
    returns (uint256)
{
    // for full liquidation, all minted amount can be liquidated
    if (_agent.status == Agent.Status.FULL_LIQUIDATION) {
        return _agent.mintedAMG;
    }
    // otherwise, liquidate just enough to get agent to safety
    uint256 targetRatioBIPS = _targetRatioBIPS(_agent, _collateralKind);
    if (targetRatioBIPS <= _collateralRatioBIPS) {
        return 0;               // agent already safe
    }
    if (_collateralRatioBIPS <= _factorBIPS) {
        return _agent.mintedAMG; // cannot achieve target - liquidate all
    }
    // Vulnerable formula: Overestimates liquidation amount
    uint256 maxLiquidatedAMG = AgentCollateral.totalBackedAMG(_agent, _collateralKind)
        .mulDivRoundUp(targetRatioBIPS - _collateralRatioBIPS, targetRatioBIPS - _factorBIPS);
    return Math.min(maxLiquidatedAMG, _agent.mintedAMG);
}
Vulnerable Line:
solidityuint256 maxLiquidatedAMG = AgentCollateral.totalBackedAMG(_agent, _collateralKind)
    .mulDivRoundUp(targetRatioBIPS - _collateralRatioBIPS, targetRatioBIPS - _factorBIPS);
Why It’s Vulnerable:

totalBackedAMG likely represents the AMG amount backable by collateral at CR=10000 BIPS (i.e., fullCollateral / amgToTokenWeiPrice). Using it as the base for the ratio (targetBIPS - currentBIPS)/(targetBIPS - factorBIPS) does not align with the correct derivation (see above).
The ratio of BIPS differences amplifies the liquidation amount incorrectly, leading to maxLiquidatedAMG being much larger than needed to reach targetRatioBIPS.
This breaks the invariant that liquidation should minimally adjust AMG to achieve the target CR, causing unfair collateral loss.

Additional Notes:

The use of mulDivRoundUp is good for protocol-favorable rounding but exacerbates the over-liquidation when the base amount is wrong.
The issue assumes totalBackedAMG is as described; if it represents something else, the formula is still incorrect per standard CR math.
The cap Math.min(maxLiquidatedAMG, _agent.mintedAMG) prevents liquidating more than minted but doesn’t fix the overestimation within bounds.