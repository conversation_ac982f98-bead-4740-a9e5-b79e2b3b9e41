You're right to suspect the formula. The bug is real — the numerator should be the total debt (mintedAMG), not the “backed capacity” (totalBackedAMG). Using totalBackedAMG overestimates how much needs to be liquidated whenever the agent has more collateral than debt (the common case), which lets liquidators take more collateral than necessary.

What the formula should be

Let (per collateral kind):

D = current debt = _agent.mintedAMG (in AMG)

C = current collateral value for this kind, in AMG

F = _factorBIPS (collateral paid per 1 AMG repaid is k = F/10000 in “AMG of collateral value”)

R = current collateral ratio in bips for this kind = 10000 * C / D (this is what _collateralRatioBIPS is measuring)

T = target ratio in bips = _targetRatioBIPS(_agent, _collateralKind)

When repaying L AMG using this collateral kind, you pay the liquidator k·L AMG worth of this collateral, so:

new debt D' = D - L

new collateral value C' = C - k·L

We need 10000 * C' / D' ≥ T, i.e.

10000(C - kL) ≥ T(D - L)
⇒ (T - 10000k)·L ≥ T·D - 10000·C


Since k = F/10000, this gives the minimal liquidation to reach target:

L* = (T·D - 10000·C) / (T - F) = D · (T - R) / (T - F)


So the correct numerator is D (the debt), not any notion of “backed AMG”.

What the code does now (and why it’s wrong)

Current code:

uint256 maxLiquidatedAMG = AgentCollateral.totalBackedAMG(_agent, _collateralKind)
    .mulDivRoundUp(targetRatioBIPS - _collateralRatioBIPS, targetRatioBIPS - _factorBIPS);
return Math.min(maxLiquidatedAMG, _agent.mintedAMG);


This computes

L_code = B · (T - R) / (T - F)


with B = totalBackedAMG(_agent, _collateralKind).

If B > D (over-collateralized for this kind), then L_code = (B/D) · L* > L* → over-liquidation allowed → value extraction / griefing.

If B < D (under-collateralized for this kind), then L_code < L* → you can’t reach target even when you hit this cap (the agent remains in liquidation). That’s a liveness/UX issue but not value-extraction.

Concrete numbers (close to your example)

Take:

amgToTokenWeiPrice = 1

fullCollateral = 120

D = mintedAMG = 109

_factorBIPS = F = 10000 (so k = 1)

R = 11000 (current CR)

T = 13000

Correct minimal:

L* = D · (T - R) / (T - F) = 109 · 2000 / 3000 = 72.666…


Code’s amount (since with F=10000, totalBackedAMG ≈ C = 120):

L_code = 120 · 2000 / 3000 = 80


Effects:

After correct L*: C' = 120 - 72.667 = 47.333, D' = 109 - 72.667 = 36.333,
CR' ≈ 10000 · 47.333 / 36.333 ≈ 13000 (on target).

After code’s 80: C' = 40, D' = 29,
CR' ≈ 10000 · 40 / 29 ≈ 13,793 — well above target with extra collateral stripped.

That’s exactly the over-liquidation you described (your “16.79” baseline assumed collateral doesn’t decrease during liquidation; here it does, at rate k = F/10000, so the correct baseline is the ~72.67 above).

Fix

Use debt in the numerator and cap by both mintedAMG and the repayable capacity of this collateral kind (so you don’t exceed what its collateral can cover):

function maxLiquidationAmountAMG(
    Agent.State storage _agent,
    uint256 _collateralRatioBIPS,
    uint256 _factorBIPS,
    Collateral.Kind _collateralKind
)
    internal view
    returns (uint256)
{
    if (_agent.status == Agent.Status.FULL_LIQUIDATION) {
        return _agent.mintedAMG;
    }
    uint256 targetRatioBIPS = _targetRatioBIPS(_agent, _collateralKind);
    if (targetRatioBIPS <= _collateralRatioBIPS) return 0;
    if (_collateralRatioBIPS <= _factorBIPS) return _agent.mintedAMG;

    // Correct minimal liquidation to hit target
    uint256 needed =
        _agent.mintedAMG.mulDivRoundUp(
            targetRatioBIPS - _collateralRatioBIPS,
            targetRatioBIPS - _factorBIPS
        );

    // Cannot repay more than the amount this collateral kind can cover
    uint256 capacity = AgentCollateral.totalBackedAMG(_agent, _collateralKind);

    return Math.min(needed, Math.min(capacity, _agent.mintedAMG));
}

Tests to add

Over-collateralized case (B > D) – ensure returned max equals L* (rounded up), not L_code.

Under-collateralized case (B < D) – ensure the returned value equals min(L*, capacity).

Boundary T == F – unreachable target: function should have already returned full liquidation (_collateralRatioBIPS <= _factorBIPS guard). Consider an explicit require(T > F) if invariants guarantee it.

Rounding – with mulDivRoundUp, confirm post-liq CR ≥ target by a hair, never below.

Severity & impact

Severity: High (economic correctness).

Impact: Enables over-liquidation and value extraction in common, over-collateralized states; liquidators can deplete more collateral than necessary and leave the agent with an unnecessarily high CR.

If you want, share AgentCollateral.totalBackedAMG and collateralRatioBIPS—I can sanity-check that capacity cap is exactly the right one for your liquidation payout path (it should match the per-AMG collateral payout implied by _factorBIPS).