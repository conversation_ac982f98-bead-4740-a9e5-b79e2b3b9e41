/**
 * Mathematical Analysis of the Liquidation Over-liquidation Bug
 * 
 * This script verifies the mathematical claims in Issue.md about the 
 * maxLiquidationAmountAMG function over-liquidating borrowers.
 */

// Helper function to simulate mulDivRoundUp
function mulDivRoundUp(a, b, c) {
    const result = (BigInt(a) * BigInt(b)) / BigInt(c);
    const remainder = (BigInt(a) * BigInt(b)) % BigInt(c);
    return remainder > 0n ? result + 1n : result;
}

// Test scenario from Issue.md
const testScenario = {
    fullCollateral: BigInt("120000000000000000000"), // 120e18
    mintedAMG: BigInt("109000000000000000000"),      // 109e18
    amgToTokenWeiPrice: BigInt("1000000000000000000"), // 1e18 (1:1 price)
    currentCR_BIPS: 11000,
    factorBIPS: 10000,  // liquidation threshold
    targetRatioBIPS: 13000  // min CR to achieve
};

console.log("=== LIQUIDATION BUG ANALYSIS ===\n");
console.log("Test Scenario:");
console.log(`- Full Collateral: ${testScenario.fullCollateral.toString()} wei (${Number(testScenario.fullCollateral) / 1e18} tokens)`);
console.log(`- Minted AMG: ${testScenario.mintedAMG.toString()} wei (${Number(testScenario.mintedAMG) / 1e18} AMG)`);
console.log(`- AMG to Token Price: ${testScenario.amgToTokenWeiPrice.toString()} wei (1:1 ratio)`);
console.log(`- Current CR: ${testScenario.currentCR_BIPS} BIPS (${testScenario.currentCR_BIPS/100}%)`);
console.log(`- Target CR: ${testScenario.targetRatioBIPS} BIPS (${testScenario.targetRatioBIPS/100}%)`);
console.log(`- Factor BIPS: ${testScenario.factorBIPS} BIPS (${testScenario.factorBIPS/100}%)\n`);

// Calculate totalBackedAMG (this represents the total AMG backed by collateral)
// Based on the code: totalBackedAMG = mintedAMG + reservedAMG + redeemingAMG
// For simplicity, assuming reservedAMG = redeemingAMG = 0
const totalBackedAMG = testScenario.mintedAMG; // Simplified case

console.log("=== CURRENT (ALLEGEDLY BUGGY) FORMULA ===");
console.log(`totalBackedAMG = ${totalBackedAMG.toString()} wei (${Number(totalBackedAMG) / 1e18} AMG)`);

// Current formula from the code:
// maxLiquidatedAMG = totalBackedAMG.mulDivRoundUp(targetRatioBIPS - collateralRatioBIPS, targetRatioBIPS - factorBIPS)
const numerator = testScenario.targetRatioBIPS - testScenario.currentCR_BIPS;
const denominator = testScenario.targetRatioBIPS - testScenario.factorBIPS;

console.log(`Numerator: ${numerator} (targetRatioBIPS - currentCR_BIPS)`);
console.log(`Denominator: ${denominator} (targetRatioBIPS - factorBIPS)`);

const currentFormulaResult = mulDivRoundUp(totalBackedAMG, numerator, denominator);
console.log(`Current Formula Result: ${currentFormulaResult.toString()} wei (${Number(currentFormulaResult) / 1e18} AMG)`);

// Calculate remaining AMG after liquidation
const remainingAMG_current = testScenario.mintedAMG - currentFormulaResult;
console.log(`Remaining AMG after liquidation: ${remainingAMG_current.toString()} wei (${Number(remainingAMG_current) / 1e18} AMG)`);

// Calculate resulting CR
const resultingCR_current = (testScenario.fullCollateral * BigInt(10000)) / (remainingAMG_current * testScenario.amgToTokenWeiPrice / BigInt("1000000000000000000"));
console.log(`Resulting CR: ${resultingCR_current.toString()} BIPS (${Number(resultingCR_current)/100}%)\n`);

console.log("=== CORRECT FORMULA (FROM ISSUE) ===");

// Correct formula from the issue:
// remainingAMG = fullCollateral * 10000 / (targetRatioBIPS * amgToTokenWeiPrice)
// liquidatedAMG = mintedAMG - remainingAMG

const correctRemainingAMG = (testScenario.fullCollateral * BigInt(10000)) / 
    (BigInt(testScenario.targetRatioBIPS) * testScenario.amgToTokenWeiPrice / BigInt("1000000000000000000"));

const correctLiquidatedAMG = testScenario.mintedAMG > correctRemainingAMG ? 
    testScenario.mintedAMG - correctRemainingAMG : BigInt(0);

console.log(`Correct Remaining AMG: ${correctRemainingAMG.toString()} wei (${Number(correctRemainingAMG) / 1e18} AMG)`);
console.log(`Correct Liquidated AMG: ${correctLiquidatedAMG.toString()} wei (${Number(correctLiquidatedAMG) / 1e18} AMG)`);

// Verify the correct formula achieves target CR
const verificationCR = (testScenario.fullCollateral * BigInt(10000)) / 
    (correctRemainingAMG * testScenario.amgToTokenWeiPrice / BigInt("1000000000000000000"));
console.log(`Verification CR: ${verificationCR.toString()} BIPS (${Number(verificationCR)/100}%)\n`);

console.log("=== COMPARISON AND BUG VERIFICATION ===");

const overLiquidationAmount = currentFormulaResult - correctLiquidatedAMG;
const overLiquidationPercentage = Number(overLiquidationAmount * BigInt(10000) / correctLiquidatedAMG) / 100;

console.log(`Over-liquidation Amount: ${overLiquidationAmount.toString()} wei (${Number(overLiquidationAmount) / 1e18} AMG)`);
console.log(`Over-liquidation Percentage: ${overLiquidationPercentage.toFixed(2)}%`);

const excessCR = Number(resultingCR_current) - testScenario.targetRatioBIPS;
console.log(`Excess CR above target: ${excessCR} BIPS (${excessCR/100}%)`);

// Assertions to verify the bug
console.log("\n=== BUG VERIFICATION RESULTS ===");

const bugExists = currentFormulaResult > correctLiquidatedAMG * BigInt(2); // At least 2x over-liquidation
console.log(`✓ Over-liquidation detected: ${bugExists}`);

const excessiveCR = Number(resultingCR_current) > testScenario.targetRatioBIPS * 2; // CR more than 2x target
console.log(`✓ Excessive CR detected: ${excessiveCR}`);

const significantOverLiquidation = overLiquidationPercentage > 100; // More than 100% over-liquidation
console.log(`✓ Significant over-liquidation (>100%): ${significantOverLiquidation}`);

if (bugExists && excessiveCR && significantOverLiquidation) {
    console.log("\n🚨 BUG CONFIRMED: The maxLiquidationAmountAMG function over-liquidates borrowers!");
    console.log("   - Liquidates significantly more AMG than necessary");
    console.log("   - Results in excessively high collateral ratios");
    console.log("   - Causes unfair losses to borrowers");
} else {
    console.log("\n✅ No significant over-liquidation bug detected in this scenario");
}

console.log("\n=== DETAILED IMPACT ANALYSIS ===");
console.log(`Expected liquidation: ${Number(correctLiquidatedAMG) / 1e18} AMG`);
console.log(`Actual liquidation: ${Number(currentFormulaResult) / 1e18} AMG`);
console.log(`Borrower loses extra: ${Number(overLiquidationAmount) / 1e18} AMG`);
console.log(`Target CR: ${testScenario.targetRatioBIPS/100}%`);
console.log(`Actual resulting CR: ${Number(resultingCR_current)/100}%`);
console.log(`CR excess: ${(Number(resultingCR_current) - testScenario.targetRatioBIPS)/100}%`);
